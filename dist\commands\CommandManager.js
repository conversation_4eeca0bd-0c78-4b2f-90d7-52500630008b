"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.commandManager = exports.CommandManager = void 0;
const discord_js_1 = require("discord.js");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const BaseCommand_1 = require("./base/BaseCommand");
const logger_1 = require("../core/logger");
const BalanceCommand_1 = require("./economy/BalanceCommand");
const PayCommand_1 = require("./economy/PayCommand");
const GiveCommand_1 = require("./admin/GiveCommand");
const BankersCommand_1 = require("./admin/BankersCommand");
const EnhanceRoleCommand_1 = require("./role/EnhanceRoleCommand");
const UpdateNamesCommand_1 = require("./role/UpdateNamesCommand");
const TradeCommand_1 = require("./trade/TradeCommand");
const TradeAdminCommand_1 = require("./admin/TradeAdminCommand");
const TradeMonitorCommand_1 = require("./admin/TradeMonitorCommand");
const SalaryAddCommand_1 = require("./admin/SalaryAddCommand");
const SalaryRemoveCommand_1 = require("./admin/SalaryRemoveCommand");
const SalaryListCommand_1 = require("./admin/SalaryListCommand");
const SuggestionSystemCommand_1 = require("./admin/SuggestionSystemCommand");
const ElectionsCommand_1 = require("./election/ElectionsCommand");
const PollCommand_1 = require("./poll/PollCommand");
class CommandManager {
    constructor() {
        this.applicationContext = null;
        this.logger = (0, logger_1.createLogger)('command-manager');
        this.registry = BaseCommand_1.commandRegistry;
        this.discordCommands = new discord_js_1.Collection();
    }
    setApplicationContext(app) {
        this.applicationContext = app;
    }
    async loadCommands() {
        this.logger.info('[CommandManager] Loading commands...');
        const stats = {
            totalLoaded: 0,
            newArchitecture: 0,
            legacyCommands: 0,
            failedLoads: 0,
            categories: {
                [BaseCommand_1.CommandCategory.ECONOMY]: 0,
                [BaseCommand_1.CommandCategory.ADMIN]: 0,
                [BaseCommand_1.CommandCategory.ROLE]: 0,
                [BaseCommand_1.CommandCategory.MILESTONE]: 0,
                [BaseCommand_1.CommandCategory.UTILITY]: 0,
                [BaseCommand_1.CommandCategory.AUTOMATION]: 0,
            },
        };
        await this.loadNewCommands(stats);
        await this.loadLegacyCommands(stats);
        this.logger.info(`[CommandManager] Command loading complete`, {
            totalLoaded: stats.totalLoaded,
            newArchitecture: stats.newArchitecture,
            legacyCommands: stats.legacyCommands,
            failedLoads: stats.failedLoads,
            categories: stats.categories,
        });
        return stats;
    }
    async loadNewCommands(stats) {
        const tradeCommand = new TradeCommand_1.TradeCommand();
        const tradeAdminCommand = new TradeAdminCommand_1.TradeAdminCommand();
        const tradeMonitorCommand = new TradeMonitorCommand_1.TradeMonitorCommand();
        if (this.applicationContext) {
            try {
                const tradeService = this.applicationContext.getService('TradeService');
                tradeCommand.setTradeService(tradeService);
                this.logger.debug('[CommandManager] Injected TradeService into TradeCommand');
                try {
                    const disputeService = this.applicationContext.getService('DisputeService');
                    const backgroundService = this.applicationContext.getService('TradeBackgroundService');
                    tradeAdminCommand.setTradeServices(tradeService, disputeService);
                    tradeMonitorCommand.setTradeServices(tradeService, disputeService, backgroundService);
                    this.logger.debug('[CommandManager] Injected services into trade admin commands');
                }
                catch (serviceError) {
                    this.logger.warn('[CommandManager] Could not inject all services into trade admin commands', { error: serviceError });
                }
            }
            catch (error) {
                this.logger.warn('[CommandManager] Could not inject TradeService into TradeCommand', { error });
            }
        }
        const pollCommand = new PollCommand_1.PollCommand();
        if (this.applicationContext) {
            try {
                const pollService = this.applicationContext.getService('PollService');
                pollCommand.setPollService(pollService);
                console.log('✅ PollService injected into PollCommand successfully');
                this.logger.debug('[CommandManager] Injected PollService into PollCommand');
            }
            catch (error) {
                console.log('❌ Failed to inject PollService:', error instanceof Error ? error.message : String(error));
                this.logger.warn('[CommandManager] Could not inject PollService into PollCommand', { error });
            }
        }
        const electionsCommand = new ElectionsCommand_1.ElectionsCommand();
        if (this.applicationContext) {
            try {
                const electionService = this.applicationContext.getService('ElectionService');
                try {
                    await electionsCommand.initialize(this.applicationContext);
                }
                catch (initError) {
                    electionsCommand.setElectionService(electionService);
                }
                console.log('✅ ElectionService injected into ElectionsCommand successfully');
                this.logger.debug('[CommandManager] Injected ElectionService into ElectionsCommand');
            }
            catch (error) {
                console.log('❌ Failed to inject ElectionService:', error instanceof Error ? error.message : String(error));
                this.logger.warn('[CommandManager] Could not inject ElectionService into ElectionsCommand', { error });
            }
        }
        const newCommands = [
            new BalanceCommand_1.BalanceCommand(),
            new PayCommand_1.PayCommand(),
            new GiveCommand_1.GiveCommand(),
            new BankersCommand_1.BankersCommand(),
            new EnhanceRoleCommand_1.EnhanceRoleCommand(),
            new UpdateNamesCommand_1.UpdateNamesCommand(),
            tradeCommand,
            tradeAdminCommand,
            tradeMonitorCommand,
            new SalaryAddCommand_1.SalaryAddCommand(),
            new SalaryRemoveCommand_1.SalaryRemoveCommand(),
            new SalaryListCommand_1.SalaryListCommand(),
            new SuggestionSystemCommand_1.SuggestionSystemCommand(),
            electionsCommand,
            pollCommand,
        ];
        for (const command of newCommands) {
            try {
                this.registry.register(command);
                this.discordCommands.set(command.data.name, command);
                stats.newArchitecture++;
                stats.totalLoaded++;
                stats.categories[command.category]++;
                this.logger.debug(`[CommandManager] Loaded new command: ${command.data.name}`);
            }
            catch (error) {
                this.logger.error(`[CommandManager] Failed to load new command: ${command.data.name}`, { error });
                stats.failedLoads++;
            }
        }
    }
    async loadLegacyCommands(stats) {
        const commandsPath = path_1.default.join(__dirname);
        const commandFiles = this.getCommandFiles(commandsPath);
        const skipFiles = new Set([
            'balance.ts', 'balance.js',
            'pay.ts', 'pay.js',
            'give.ts', 'give.js',
            'enhancerole.ts', 'enhancerole.js',
            'updatenames.ts', 'updatenames.js',
        ]);
        for (const file of commandFiles) {
            if (skipFiles.has(path_1.default.basename(file))) {
                this.logger.debug(`[CommandManager] Skipping ${file} (handled by new architecture)`);
                continue;
            }
            try {
                const command = require(file);
                if (command.data && command.execute) {
                    this.discordCommands.set(command.data.name, command);
                    stats.legacyCommands++;
                    stats.totalLoaded++;
                    const category = this.categorizeLegacyCommand(command.data.name);
                    if (category) {
                        stats.categories[category]++;
                    }
                    this.logger.debug(`[CommandManager] Loaded legacy command: ${command.data.name}`);
                }
                else {
                    this.logger.warn(`[CommandManager] Invalid command file: ${file}`);
                }
            }
            catch (error) {
                this.logger.error(`[CommandManager] Failed to load legacy command: ${file}`, { error });
                stats.failedLoads++;
            }
        }
    }
    getCommandFiles(dir) {
        const files = [];
        try {
            const items = fs_1.default.readdirSync(dir);
            for (const item of items) {
                const fullPath = path_1.default.join(dir, item);
                const stat = fs_1.default.statSync(fullPath);
                if (stat.isDirectory()) {
                    if (['base', 'economy', 'admin', 'role', 'milestone', 'utility', 'automation'].includes(item)) {
                        continue;
                    }
                    files.push(...this.getCommandFiles(fullPath));
                }
                else if (item.endsWith('.ts') || item.endsWith('.js')) {
                    if (!item.includes('index') && !item.includes('Manager') && !item.includes('Base')) {
                        files.push(fullPath);
                    }
                }
            }
        }
        catch (error) {
            this.logger.error(`[CommandManager] Error reading directory: ${dir}`, { error });
        }
        return files;
    }
    categorizeLegacyCommand(commandName) {
        const economyCommands = ['history', 'leaderboard'];
        const adminCommands = ['fine', 'announce', 'tax', 'starterbalance', 'testcleanup', 'incomecredentials'];
        const roleCommands = ['roles', 'addrole', 'editrole', 'removerole', 'richestrole', 'enhancerole', 'updatenames'];
        const milestoneCommands = ['milestone', 'milestones', 'milestonestatus'];
        const utilityCommands = ['help', 'placeholders'];
        const automationCommands = ['automessage', 'editautomessage', 'monetizechannel'];
        if (economyCommands.includes(commandName))
            return BaseCommand_1.CommandCategory.ECONOMY;
        if (adminCommands.includes(commandName))
            return BaseCommand_1.CommandCategory.ADMIN;
        if (roleCommands.includes(commandName))
            return BaseCommand_1.CommandCategory.ROLE;
        if (milestoneCommands.includes(commandName))
            return BaseCommand_1.CommandCategory.MILESTONE;
        if (utilityCommands.includes(commandName))
            return BaseCommand_1.CommandCategory.UTILITY;
        if (automationCommands.includes(commandName))
            return BaseCommand_1.CommandCategory.AUTOMATION;
        return null;
    }
    getDiscordCommands() {
        return this.discordCommands;
    }
    getRegistry() {
        return this.registry;
    }
    getCommand(name) {
        return this.discordCommands.get(name);
    }
    getCommandsByCategory(category) {
        return this.registry.getByCategory(category);
    }
    getStats() {
        return {
            totalCommands: this.discordCommands.size,
            newArchitectureCommands: this.registry.getCount(),
            categories: this.registry.getCategories().map(cat => ({
                category: cat,
                count: this.registry.getByCategory(cat).length,
            })),
        };
    }
    clear() {
        this.discordCommands.clear();
        this.registry.clear();
        this.logger.debug('[CommandManager] Cleared all commands');
    }
}
exports.CommandManager = CommandManager;
exports.commandManager = new CommandManager();
exports.default = CommandManager;
