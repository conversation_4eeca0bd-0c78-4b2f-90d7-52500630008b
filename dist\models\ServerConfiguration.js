"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const ServerConfigurationSchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    nationName: {
        type: String,
        required: true,
        default: 'Phalanx Order',
        maxlength: 100,
        trim: true
    },
    coinName: {
        type: String,
        required: true,
        default: 'Phalanx Loyalty Coins',
        maxlength: 50,
        trim: true
    },
    coinSymbol: {
        type: String,
        required: true,
        default: 'PLC',
        maxlength: 10,
        trim: true
    },
    embedColor: {
        type: String,
        required: true,
        default: '#DD7D00',
        validate: {
            validator: function (v) {
                const trimmedColor = v.trim();
                const lowerColor = trimmedColor.toLowerCase();
                const colorNames = [
                    'red', 'green', 'blue', 'yellow', 'orange', 'purple', 'pink',
                    'cyan', 'magenta', 'lime', 'indigo', 'violet', 'brown', 'black',
                    'white', 'gray', 'grey', 'gold', 'silver', 'navy', 'teal', 'maroon',
                    'aqua', 'fuchsia', 'olive', 'darkred', 'darkgreen', 'darkblue',
                    'lightgray', 'lightgrey', 'darkgray', 'darkgrey', 'lightblue',
                    'lightgreen', 'lightyellow', 'lightpink', 'lightcyan', 'darkviolet',
                    'darkorange', 'darkgoldenrod', 'mediumblue', 'mediumseagreen',
                    'mediumorchid', 'mediumturquoise', 'mediumvioletred', 'royalblue',
                    'steelblue', 'slateblue', 'springgreen', 'forestgreen', 'seagreen',
                    'crimson', 'coral', 'salmon', 'tomato', 'orangered', 'hotpink',
                    'deeppink', 'palevioletred', 'plum', 'orchid', 'thistle', 'lavender'
                ];
                if (colorNames.includes(lowerColor)) {
                    return true;
                }
                const hexPattern6 = /^#?[A-Fa-f0-9]{6}$/;
                const hexPattern3 = /^#?[A-Fa-f0-9]{3}$/;
                return hexPattern6.test(trimmedColor) || hexPattern3.test(trimmedColor);
            },
            message: 'Embed color must be a valid hex color (#FF0000, FF0000, #F00, F00) or color name (red, blue, etc.)'
        }
    },
    bankerRoleId: {
        type: String,
        required: false,
        default: null,
        validate: {
            validator: function (v) {
                if (!v)
                    return true;
                return /^\d{17,20}$/.test(v.trim());
            },
            message: 'Banker role ID must be a valid Discord snowflake'
        }
    }
}, {
    timestamps: true,
    collection: 'serverconfigurations'
});
ServerConfigurationSchema.index({ guildId: 1 });
ServerConfigurationSchema.statics.findByGuildId = function (guildId) {
    return this.findOne({ guildId });
};
ServerConfigurationSchema.statics.createOrUpdate = async function (guildId, updates) {
    return this.findOneAndUpdate({ guildId }, { ...updates, guildId }, { upsert: true, new: true, runValidators: true });
};
ServerConfigurationSchema.methods.getDisplayName = function () {
    return `${this.nationName} (${this.coinSymbol})`;
};
ServerConfigurationSchema.methods.getFormattedCoinName = function (amount) {
    const plural = amount === 1 ? this.coinName.replace(/s$/, '') : this.coinName;
    return `${amount.toLocaleString()} ${plural}`;
};
const ServerConfiguration = mongoose_1.default.model('ServerConfiguration', ServerConfigurationSchema);
exports.default = ServerConfiguration;
