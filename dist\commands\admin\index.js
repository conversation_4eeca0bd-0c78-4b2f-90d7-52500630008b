"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.incomecredentials = exports.testcleanup = exports.starterbalance = exports.tax = exports.announce = exports.fine = exports.give = exports.SuggestionSystemCommand = exports.SalaryListCommand = exports.SalaryRemoveCommand = exports.SalaryAddCommand = exports.TradeMonitorCommand = exports.TradeAdminCommand = exports.BankersCommand = exports.GiveCommand = void 0;
var GiveCommand_1 = require("./GiveCommand");
Object.defineProperty(exports, "GiveCommand", { enumerable: true, get: function () { return GiveCommand_1.GiveCommand; } });
var BankersCommand_1 = require("./BankersCommand");
Object.defineProperty(exports, "BankersCommand", { enumerable: true, get: function () { return BankersCommand_1.BankersCommand; } });
var TradeAdminCommand_1 = require("./TradeAdminCommand");
Object.defineProperty(exports, "TradeAdminCommand", { enumerable: true, get: function () { return TradeAdminCommand_1.TradeAdminCommand; } });
var TradeMonitorCommand_1 = require("./TradeMonitorCommand");
Object.defineProperty(exports, "TradeMonitorCommand", { enumerable: true, get: function () { return TradeMonitorCommand_1.TradeMonitorCommand; } });
var SalaryAddCommand_1 = require("./SalaryAddCommand");
Object.defineProperty(exports, "SalaryAddCommand", { enumerable: true, get: function () { return SalaryAddCommand_1.SalaryAddCommand; } });
var SalaryRemoveCommand_1 = require("./SalaryRemoveCommand");
Object.defineProperty(exports, "SalaryRemoveCommand", { enumerable: true, get: function () { return SalaryRemoveCommand_1.SalaryRemoveCommand; } });
var SalaryListCommand_1 = require("./SalaryListCommand");
Object.defineProperty(exports, "SalaryListCommand", { enumerable: true, get: function () { return SalaryListCommand_1.SalaryListCommand; } });
var SuggestionSystemCommand_1 = require("./SuggestionSystemCommand");
Object.defineProperty(exports, "SuggestionSystemCommand", { enumerable: true, get: function () { return SuggestionSystemCommand_1.SuggestionSystemCommand; } });
exports.give = require('../give');
exports.fine = require('../fine');
exports.announce = require('../announce');
exports.tax = require('../tax');
exports.starterbalance = require('../starterbalance');
exports.testcleanup = require('../testcleanup');
exports.incomecredentials = require('../incomecredentials');
